/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 10:13:55 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
  A class for the Servicemanager ASN.1/Java  project.
*/
public class Servicemanager extends ASN1Project {

    /**
     * Initialize the pdu decoder.
     */
    private static final ProjectInfo c_projectInfo = new ProjectInfo (
	null,
	new byte[] {
	    (byte)0x0b, (byte)0x35, (byte)0x61, (byte)0xd9, (byte)0xf3,
	    (byte)0x54, (byte)0xea, (byte)0x0c, (byte)0x68, (byte)0x54,
	    (byte)0x80, (byte)0x47, (byte)0x00, (byte)0xe7, (byte)0x6f,
	    (byte)0x56, (byte)0xac, (byte)0x97, (byte)0xb5, (byte)0x51,
	    (byte)0xd7, (byte)0x31, (byte)0x82, (byte)0x9d, (byte)0xe9,
	    (byte)0xe9, (byte)0xe2, (byte)0x79, (byte)0x22, (byte)0x33,
	    (byte)0x02, (byte)0xa5, (byte)0xab, (byte)0xa2, (byte)0x31,
	    (byte)0x0f, (byte)0xdb, (byte)0x4f, (byte)0x93, (byte)0x9b,
	    (byte)0xa1, (byte)0x6b, (byte)0xe0, (byte)0x0a, (byte)0x52,
	    (byte)0xa1, (byte)0x45, (byte)0xd0, (byte)0x17, (byte)0x90,
	    (byte)0x57, (byte)0xbf, (byte)0x3e, (byte)0x60, (byte)0x93,
	    (byte)0x22, (byte)0x13, (byte)0xa9, (byte)0x81, (byte)0xe9,
	    (byte)0x9d, (byte)0x82, (byte)0x31, (byte)0xdf, (byte)0x30,
	    (byte)0x10, (byte)0x42, (byte)0x09, (byte)0xdd, (byte)0xca,
	    (byte)0x7c, (byte)0x18, (byte)0xff, (byte)0xf3, (byte)0xec,
	    (byte)0xf1, (byte)0x77, (byte)0x0d, (byte)0xcc
	},
	"2025/06/12"
    );
    
    /**
     * Get the project descriptor of 'this' object.
     */
    public ProjectInfo getProjectInfo()
    {
	return c_projectInfo;
    }
    
    private static final ASN1Project c_project = new Servicemanager();

    /**
     * Methods for accessing Coders.
     */
    public static Coder getDefaultCoder()
    {
	return createCOERCoder(c_project);
    }
    
    public static OERCoder getOERCoder()
    {
	return createOERCoder(c_project);
    }
    
    public static COERCoder getCOERCoder()
    {
	return createCOERCoder(c_project);
    }
    
}
