/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 09:51:19 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
  A class for the Servicemanager ASN.1/Java  project.
*/
public class Servicemanager extends ASN1Project {

    /**
     * Initialize the pdu decoder.
     */
    private static final ProjectInfo c_projectInfo = new ProjectInfo (
	null,
	new byte[] {
	    (byte)0x0b, (byte)0xec, (byte)0x9c, (byte)0xc9, (byte)0x46,
	    (byte)0x8d, (byte)0x17, (byte)0x1c, (byte)0xdd, (byte)0x8d,
	    (byte)0x7d, (byte)0x57, (byte)0xb5, (byte)0x2d, (byte)0x77,
	    (byte)0xad, (byte)0xb3, (byte)0x36, (byte)0x84, (byte)0x4b,
	    (byte)0x51, (byte)0x55, (byte)0x0e, (byte)0x1b, (byte)0x99,
	    (byte)0x81, (byte)0x43, (byte)0x32, (byte)0x4f, (byte)0x89,
	    (byte)0x8c, (byte)0xa2, (byte)0x18, (byte)0x0f, (byte)0x68,
	    (byte)0xdd, (byte)0x32, (byte)0x49, (byte)0xf4, (byte)0x36,
	    (byte)0xad, (byte)0x2a, (byte)0xc5, (byte)0xd6, (byte)0x85,
	    (byte)0x53, (byte)0x44, (byte)0xa4, (byte)0xe3, (byte)0xb1,
	    (byte)0x8b, (byte)0x37, (byte)0x86, (byte)0x4f, (byte)0x58,
	    (byte)0x08, (byte)0xe9, (byte)0x99, (byte)0x1b, (byte)0x0e,
	    (byte)0x55, (byte)0x59, (byte)0x2a, (byte)0x21, (byte)0xe3,
	    (byte)0x16, (byte)0x26, (byte)0xd2, (byte)0xb6, (byte)0xa0,
	    (byte)0xcd, (byte)0x3d, (byte)0x07, (byte)0x3f, (byte)0xd0,
	    (byte)0xed, (byte)0x8e
	},
	"2025/06/12"
    );
    
    /**
     * Get the project descriptor of 'this' object.
     */
    public ProjectInfo getProjectInfo()
    {
	return c_projectInfo;
    }
    
    private static final ASN1Project c_project = new Servicemanager();

    /**
     * Methods for accessing Coders.
     */
    public static Coder getDefaultCoder()
    {
	return createCOERCoder(c_project);
    }
    
    public static OERCoder getOERCoder()
    {
	return createOERCoder(c_project);
    }
    
    public static COERCoder getCOERCoder()
    {
	return createCOERCoder(c_project);
    }
    
}
