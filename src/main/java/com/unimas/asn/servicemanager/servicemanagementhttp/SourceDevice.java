/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 09:51:19 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the SourceDevice ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class SourceDevice extends Sequence {
    
    /**
     * The default constructor.
     */
    public SourceDevice()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public SourceDevice(IPAddress ipAddress, INTEGER port)
    {
	setIpAddress(ipAddress);
	setPort(port);
    }
    
    /**
     * Construct with components.
     */
    public SourceDevice(IPAddress ipAddress, long port)
    {
	this(ipAddress, new INTEGER(port));
    }
    
    /**
     * Construct with required components.
     */
    public SourceDevice(IPAddress ipAddress)
    {
	setIpAddress(ipAddress);
    }
    
    public void initComponents()
    {
	mComponents[0] = new IPAddress();
	mComponents[1] = new INTEGER();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new IPAddress();
	    case 1:
		return new INTEGER();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "ipAddress"
    public IPAddress getIpAddress()
    {
	return (IPAddress)mComponents[0];
    }
    
    public void setIpAddress(IPAddress ipAddress)
    {
	mComponents[0] = ipAddress;
    }
    
    
    // Methods for field "port"
    public long getPort()
    {
	return ((INTEGER)mComponents[1]).longValue();
    }
    
    public void setPort(long port)
    {
	setPort(new INTEGER(port));
    }
    
    public void setPort(INTEGER port)
    {
	mComponents[1] = port;
    }
    
    public boolean hasPort()
    {
	return componentIsPresent(1);
    }
    
    public void deletePort()
    {
	setComponentAbsent(1);
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "SourceDevice"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "SourceDevice"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "ipAddress",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"INTEGER"
			    ),
			    new QName (
				"builtin",
				"INTEGER"
			    ),
			    536603,
			    null,
			    null,
			    null,
			    0
			)
		    ),
		    "port",
		    1,
		    3,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' SourceDevice object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' SourceDevice object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for SourceDevice
