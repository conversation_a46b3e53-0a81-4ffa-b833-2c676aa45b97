/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue <PERSON>  3 10:13:55 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Uint64 ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see HugeInteger
 */

public class Uint64 extends HugeInteger {
    
    /**
     * The default constructor.
     */
    public Uint64()
    {
    }
    
    public Uint64(java.math.BigInteger value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final HugeIntegerInfo c_typeinfo = new HugeIntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "Uint64"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Uint64"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new Uint64(new java.math.BigInteger("0")), 
		new Uint64(new java.math.BigInteger("18446744073709551615")),
		0
	    )
	),
	null,
	null,
	8,
	null
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' Uint64 object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' Uint64 object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for Uint64
