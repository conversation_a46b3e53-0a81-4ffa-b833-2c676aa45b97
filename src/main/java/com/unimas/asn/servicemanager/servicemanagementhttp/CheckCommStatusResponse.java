/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 09:51:19 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the CheckCommStatusResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class CheckCommStatusResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public CheckCommStatusResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public CheckCommStatusResponse(ContentMessageType messageType, 
		    ServiceId serviceId, Network network, BOOLEAN isConnected, 
		    Uint64 connectionEventTime)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setNetwork(network);
	setIsConnected(isConnected);
	setConnectionEventTime(connectionEventTime);
    }
    
    /**
     * Construct with components.
     */
    public CheckCommStatusResponse(ContentMessageType messageType, 
		    ServiceId serviceId, Network network, boolean isConnected, 
		    Uint64 connectionEventTime)
    {
	this(messageType, serviceId, network, new BOOLEAN(isConnected), 
	     connectionEventTime);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = new ServiceId();
	mComponents[2] = Network.sender;
	mComponents[3] = new BOOLEAN();
	mComponents[4] = new Uint64();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[5];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return new ServiceId();
	    case 2:
		return Network.sender;
	    case 3:
		return new BOOLEAN();
	    case 4:
		return new Uint64();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "serviceId"
    public ServiceId getServiceId()
    {
	return (ServiceId)mComponents[1];
    }
    
    public void setServiceId(ServiceId serviceId)
    {
	mComponents[1] = serviceId;
    }
    
    
    // Methods for field "network"
    public Network getNetwork()
    {
	return (Network)mComponents[2];
    }
    
    public void setNetwork(Network network)
    {
	mComponents[2] = network;
    }
    
    
    // Methods for field "isConnected"
    public boolean getIsConnected()
    {
	return ((BOOLEAN)mComponents[3]).booleanValue();
    }
    
    public void setIsConnected(boolean isConnected)
    {
	setIsConnected(new BOOLEAN(isConnected));
    }
    
    public void setIsConnected(BOOLEAN isConnected)
    {
	mComponents[3] = isConnected;
    }
    
    
    // Methods for field "connectionEventTime"
    public Uint64 getConnectionEventTime()
    {
	return (Uint64)mComponents[4];
    }
    
    public void setConnectionEventTime(Uint64 connectionEventTime)
    {
	mComponents[4] = connectionEventTime;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "CheckCommStatusResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "CheckCommStatusResponse"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					8
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					9
				    ),
				    new MemberListElement (
					"sendPacketStats",
					10
				    ),
				    new MemberListElement (
					"receivePacketStats",
					11
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					12
				    ),
				    new MemberListElement (
					"hostMngService",
					13
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					14
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ServiceId"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ServiceId"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new ServiceId(0), 
				    new ServiceId(32767),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(32767)
			    ),
			    null,
			    2
			)
		    ),
		    "serviceId",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Network"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Network"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"sender",
					0
				    ),
				    new MemberListElement (
					"receiver",
					1
				    )
				}
			    ),
			    0,
			    Network.sender
			)
		    ),
		    "network",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new TypeInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"BOOLEAN"
			    ),
			    new QName (
				"builtin",
				"BOOLEAN"
			    ),
			    536603,
			    null
			)
		    ),
		    "isConnected",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "connectionEventTime",
		    4,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' CheckCommStatusResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' CheckCommStatusResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for CheckCommStatusResponse
