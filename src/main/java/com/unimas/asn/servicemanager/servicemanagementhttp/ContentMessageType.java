/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue <PERSON>  3 10:13:55 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ContentMessageType ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Enumerated
 */

public final class ContentMessageType extends Enumerated {
    
    /**
     * The default constructor.
     */
    private ContentMessageType()
    {
	super(cFirstNumber);
    }
    
    protected ContentMessageType(long value)
    {
	super(value);
    }
    
    /**
      An inner class that contains numeric values for ASN.1 ENUMERATED type.
      The values can be used in switch/case statements.
    */
    public static final class Value {
	public static final long setInterfaceIpService = 0;
	public static final long addService = 1;
	public static final long updateService = 2;
	public static final long deleteService = 3;
	public static final long controlService = 4;
	public static final long queryServiceStatus = 5;
	public static final long queryServiceConfig = 6;
	public static final long reportAlarm = 7;
	public static final long queryWorkStatus = 8;
	public static final long getAllServiceIds = 9;
	public static final long sendPacketStats = 10;
	public static final long receivePacketStats = 11;
	public static final long checkCommStatusService = 12;
	public static final long hostMngService = 13;
	public static final long sourceDeviceService = 14;
	
    }
    // Named list definitions.
    private final static ContentMessageType cNamedNumbers[] = {
	new ContentMessageType(), 
	new ContentMessageType(1), 
	new ContentMessageType(2), 
	new ContentMessageType(3), 
	new ContentMessageType(4), 
	new ContentMessageType(5), 
	new ContentMessageType(6), 
	new ContentMessageType(7), 
	new ContentMessageType(8), 
	new ContentMessageType(9), 
	new ContentMessageType(10), 
	new ContentMessageType(11), 
	new ContentMessageType(12), 
	new ContentMessageType(13), 
	new ContentMessageType(14)
    };
    public static final ContentMessageType setInterfaceIpService = cNamedNumbers[0];
    public static final ContentMessageType addService = cNamedNumbers[1];
    public static final ContentMessageType updateService = cNamedNumbers[2];
    public static final ContentMessageType deleteService = cNamedNumbers[3];
    public static final ContentMessageType controlService = cNamedNumbers[4];
    public static final ContentMessageType queryServiceStatus = cNamedNumbers[5];
    public static final ContentMessageType queryServiceConfig = cNamedNumbers[6];
    public static final ContentMessageType reportAlarm = cNamedNumbers[7];
    public static final ContentMessageType queryWorkStatus = cNamedNumbers[8];
    public static final ContentMessageType getAllServiceIds = cNamedNumbers[9];
    public static final ContentMessageType sendPacketStats = cNamedNumbers[10];
    public static final ContentMessageType receivePacketStats = cNamedNumbers[11];
    public static final ContentMessageType checkCommStatusService = cNamedNumbers[12];
    public static final ContentMessageType hostMngService = cNamedNumbers[13];
    public static final ContentMessageType sourceDeviceService = cNamedNumbers[14];
    
    protected final static long cFirstNumber = 0;
    protected final static boolean cLinearNumbers = false;
    
    public Enumerated[] getNamedNumbers()
    {
	return cNamedNumbers;
    }
    
    public boolean hasLinearNumbers()
    {
	return cLinearNumbers;
    }
    
    public long getFirstNumber()
    {
	return cFirstNumber;
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public static int indexOfValue(long value)
    {
	if (value >= 0 && value <= 14)
	    return (int)value;
	else
	    return -1;
    }
    
    /**
     * Returns an enumerator with a specified value or null if the value
     * is not associated with any enumerators.
     *  @param value the value of the enumerator to return.
     *  @return an enumerator with a specified value.
     */
    
    public static ContentMessageType valueOf(long value)
    {
	int inx = indexOfValue(value);
	
	if (inx < 0)
	    return null;
	else
	    return cNamedNumbers[inx];
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public int indexOf()
    {
	if (isUnknownEnumerator())
	    return -1;
	return indexOfValue(mValue);
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public Enumerated lookupValue(long value)
    {
	return valueOf(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final EnumeratedInfo c_typeinfo = new EnumeratedInfo (
	new Tags (
	    new short[] {
		0x000a
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ContentMessageType"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ContentMessageType"
	),
	536607,
	null,
	new MemberList (
	    new MemberListElement[] {
		new MemberListElement (
		    "setInterfaceIpService",
		    0
		),
		new MemberListElement (
		    "addService",
		    1
		),
		new MemberListElement (
		    "updateService",
		    2
		),
		new MemberListElement (
		    "deleteService",
		    3
		),
		new MemberListElement (
		    "controlService",
		    4
		),
		new MemberListElement (
		    "queryServiceStatus",
		    5
		),
		new MemberListElement (
		    "queryServiceConfig",
		    6
		),
		new MemberListElement (
		    "reportAlarm",
		    7
		),
		new MemberListElement (
		    "queryWorkStatus",
		    8
		),
		new MemberListElement (
		    "getAllServiceIds",
		    9
		),
		new MemberListElement (
		    "sendPacketStats",
		    10
		),
		new MemberListElement (
		    "receivePacketStats",
		    11
		),
		new MemberListElement (
		    "checkCommStatusService",
		    12
		),
		new MemberListElement (
		    "hostMngService",
		    13
		),
		new MemberListElement (
		    "sourceDeviceService",
		    14
		)
	    }
	),
	0,
	setInterfaceIpService
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ContentMessageType object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ContentMessageType object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Methods for "unknownEnumerator"
     */
    private static final ContentMessageType cUnknownEnumerator = 
	new ContentMessageType(-1);
    
    public boolean isUnknownEnumerator()
    {
	return this == cUnknownEnumerator;
    }
    
    public Enumerated getUnknownEnumerator()
    {
	return cUnknownEnumerator;
    }
    
} // End class definition for ContentMessageType
