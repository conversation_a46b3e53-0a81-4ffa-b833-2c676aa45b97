/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue <PERSON>  3 10:13:55 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ProcessErrorState ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Enumerated
 */

public final class ProcessErrorState extends Enumerated {
    
    /**
     * The default constructor.
     */
    private ProcessErrorState()
    {
	super(cFirstNumber);
    }
    
    protected ProcessErrorState(long value)
    {
	super(value);
    }
    
    /**
      An inner class that contains numeric values for ASN.1 ENUMERATED type.
      The values can be used in switch/case statements.
    */
    public static final class Value {
	public static final long messageStructureError = 0;
	public static final long serviceNumLimitError = 1;
	public static final long displayNameConflictError = 2;
	public static final long illegalArgumentError = 3;
	public static final long serviceStatusError = 4;
	public static final long serviceNotExistError = 5;
	public static final long illegalOperationError = 6;
	public static final long sessionTimeout = 7;
	
    }
    // Named list definitions.
    private final static ProcessErrorState cNamedNumbers[] = {
	new ProcessErrorState(), 
	new ProcessErrorState(1), 
	new ProcessErrorState(2), 
	new ProcessErrorState(3), 
	new ProcessErrorState(4), 
	new ProcessErrorState(5), 
	new ProcessErrorState(6), 
	new ProcessErrorState(7)
    };
    public static final ProcessErrorState messageStructureError = cNamedNumbers[0];
    public static final ProcessErrorState serviceNumLimitError = cNamedNumbers[1];
    public static final ProcessErrorState displayNameConflictError = cNamedNumbers[2];
    public static final ProcessErrorState illegalArgumentError = cNamedNumbers[3];
    public static final ProcessErrorState serviceStatusError = cNamedNumbers[4];
    public static final ProcessErrorState serviceNotExistError = cNamedNumbers[5];
    public static final ProcessErrorState illegalOperationError = cNamedNumbers[6];
    public static final ProcessErrorState sessionTimeout = cNamedNumbers[7];
    
    protected final static long cFirstNumber = 0;
    protected final static boolean cLinearNumbers = false;
    
    public Enumerated[] getNamedNumbers()
    {
	return cNamedNumbers;
    }
    
    public boolean hasLinearNumbers()
    {
	return cLinearNumbers;
    }
    
    public long getFirstNumber()
    {
	return cFirstNumber;
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public static int indexOfValue(long value)
    {
	if (value >= 0 && value <= 7)
	    return (int)value;
	else
	    return -1;
    }
    
    /**
     * Returns an enumerator with a specified value or null if the value
     * is not associated with any enumerators.
     *  @param value the value of the enumerator to return.
     *  @return an enumerator with a specified value.
     */
    
    public static ProcessErrorState valueOf(long value)
    {
	int inx = indexOfValue(value);
	
	if (inx < 0)
	    return null;
	else
	    return cNamedNumbers[inx];
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public int indexOf()
    {
	if (isUnknownEnumerator())
	    return -1;
	return indexOfValue(mValue);
    }
    
    /**
     * This method is reserved for internal use and must not be invoked from the application code.
     * @exclude
     */
    public Enumerated lookupValue(long value)
    {
	return valueOf(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final EnumeratedInfo c_typeinfo = new EnumeratedInfo (
	new Tags (
	    new short[] {
		0x000a
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ProcessErrorState"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ProcessErrorState"
	),
	536607,
	null,
	new MemberList (
	    new MemberListElement[] {
		new MemberListElement (
		    "messageStructureError",
		    0
		),
		new MemberListElement (
		    "serviceNumLimitError",
		    1
		),
		new MemberListElement (
		    "displayNameConflictError",
		    2
		),
		new MemberListElement (
		    "illegalArgumentError",
		    3
		),
		new MemberListElement (
		    "serviceStatusError",
		    4
		),
		new MemberListElement (
		    "serviceNotExistError",
		    5
		),
		new MemberListElement (
		    "illegalOperationError",
		    6
		),
		new MemberListElement (
		    "sessionTimeout",
		    7
		)
	    }
	),
	0,
	messageStructureError
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ProcessErrorState object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ProcessErrorState object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Methods for "unknownEnumerator"
     */
    private static final ProcessErrorState cUnknownEnumerator = 
	new ProcessErrorState(-1);
    
    public boolean isUnknownEnumerator()
    {
	return this == cUnknownEnumerator;
    }
    
    public Enumerated getUnknownEnumerator()
    {
	return cUnknownEnumerator;
    }
    
} // End class definition for ProcessErrorState
