/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 09:51:19 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Int8 ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see INTEGER
 */

public class Int8 extends INTEGER {
    
    /**
     * The default constructor.
     */
    public Int8()
    {
    }
    
    public Int8(short value)
    {
	super(value);
    }
    
    public Int8(int value)
    {
	super(value);
    }
    
    public Int8(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "Int8"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Int8"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new Int8(-128), 
		new Int8(127),
		0
	    )
	),
	new Bounds (
	    java.lang.Long.valueOf(-128),
	    java.lang.Long.valueOf(127)
	),
	null,
	1
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' Int8 object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' Int8 object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for Int8
