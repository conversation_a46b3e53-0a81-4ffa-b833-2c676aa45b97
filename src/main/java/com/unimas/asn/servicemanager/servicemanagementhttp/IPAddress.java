/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 09:51:19 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the IPAddress ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Choice
 */

public class IPAddress extends Choice {
    
    /**
     * The default constructor.
     */
    public IPAddress()
    {
    }
    
    public static final  int  ipV4_chosen = 1;
    public static final  int  ipV6_chosen = 2;
    
    // Methods for field "ipV4"
    public static IPAddress createIPAddressWithIpV4(IPv4Address ipV4)
    {
	IPAddress __object = new IPAddress();

	__object.setIpV4(ipV4);
	return __object;
    }
    
    public boolean hasIpV4()
    {
	return getChosenFlag() == ipV4_chosen;
    }
    
    public IPv4Address getIpV4()
    {
	if (hasIpV4())
	    return (IPv4Address)mChosenValue;
	else
	    return null;
    }
    
    public void setIpV4(IPv4Address ipV4)
    {
	setChosenValue(ipV4);
	setChosenFlag(ipV4_chosen);
    }
    
    
    // Methods for field "ipV6"
    public static IPAddress createIPAddressWithIpV6(IPv6Address ipV6)
    {
	IPAddress __object = new IPAddress();

	__object.setIpV6(ipV6);
	return __object;
    }
    
    public boolean hasIpV6()
    {
	return getChosenFlag() == ipV6_chosen;
    }
    
    public IPv6Address getIpV6()
    {
	if (hasIpV6())
	    return (IPv6Address)mChosenValue;
	else
	    return null;
    }
    
    public void setIpV6(IPv6Address ipV6)
    {
	setChosenValue(ipV6);
	setChosenFlag(ipV6_chosen);
    }
    
    
    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case ipV4_chosen:
		return new IPv4Address();
	    case ipV6_chosen:
		return new IPv6Address();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "IPAddress"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "IPAddress"
	),
	536603,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPv4Address"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPv4Address"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new com.oss.asn1.INTEGER(4)
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(4),
				java.lang.Long.valueOf(4)
			    )
			)
		    ),
		    "ipV4",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPv6Address"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPv6Address"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new com.oss.asn1.INTEGER(16)
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(16),
				java.lang.Long.valueOf(16)
			    )
			)
		    ),
		    "ipV6",
		    1,
		    2
		)
	    }
	),
	0,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' IPAddress object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' IPAddress object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for IPAddress
