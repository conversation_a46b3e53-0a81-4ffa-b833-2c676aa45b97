/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 09:51:19 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ErrorResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class ErrorResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public ErrorResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public ErrorResponse(ContentMessageType messageType, 
		    ProcessErrorState errorState)
    {
	setMessageType(messageType);
	setErrorState(errorState);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = ProcessErrorState.messageStructureError;
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return ProcessErrorState.messageStructureError;
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "errorState"
    public ProcessErrorState getErrorState()
    {
	return (ProcessErrorState)mComponents[1];
    }
    
    public void setErrorState(ProcessErrorState errorState)
    {
	mComponents[1] = errorState;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ErrorResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ErrorResponse"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					8
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					9
				    ),
				    new MemberListElement (
					"sendPacketStats",
					10
				    ),
				    new MemberListElement (
					"receivePacketStats",
					11
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					12
				    ),
				    new MemberListElement (
					"hostMngService",
					13
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					14
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ProcessErrorState"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ProcessErrorState"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"messageStructureError",
					0
				    ),
				    new MemberListElement (
					"serviceNumLimitError",
					1
				    ),
				    new MemberListElement (
					"displayNameConflictError",
					2
				    ),
				    new MemberListElement (
					"illegalArgumentError",
					3
				    ),
				    new MemberListElement (
					"serviceStatusError",
					4
				    ),
				    new MemberListElement (
					"serviceNotExistError",
					5
				    ),
				    new MemberListElement (
					"illegalOperationError",
					6
				    ),
				    new MemberListElement (
					"sessionTimeout",
					7
				    )
				}
			    ),
			    0,
			    ProcessErrorState.messageStructureError
			)
		    ),
		    "errorState",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ErrorResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ErrorResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for ErrorResponse
