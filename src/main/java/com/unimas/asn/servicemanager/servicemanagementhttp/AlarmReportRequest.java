/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 10:13:55 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the AlarmReportRequest ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class AlarmReportRequest extends Sequence {
    
    /**
     * The default constructor.
     */
    public AlarmReportRequest()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public AlarmReportRequest(ContentMessageType messageType, 
		    ServiceId serviceId, AlarmType alarmType, 
		    AlarmCode alarmCode, AlarmStatus alarmStatus, 
		    Uint64 happenTime, Uint64 resolvedTime, 
		    IA5String alarmDesc)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setAlarmType(alarmType);
	setAlarmCode(alarmCode);
	setAlarmStatus(alarmStatus);
	setHappenTime(happenTime);
	setResolvedTime(resolvedTime);
	setAlarmDesc(alarmDesc);
    }
    
    /**
     * Construct with required components.
     */
    public AlarmReportRequest(ContentMessageType messageType, 
		    ServiceId serviceId, AlarmType alarmType, 
		    AlarmCode alarmCode, AlarmStatus alarmStatus)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setAlarmType(alarmType);
	setAlarmCode(alarmCode);
	setAlarmStatus(alarmStatus);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = new ServiceId();
	mComponents[2] = AlarmType.faultAlarm;
	mComponents[3] = AlarmCode.illegalCertificate;
	mComponents[4] = AlarmStatus.alarmRaised;
	mComponents[5] = new Uint64();
	mComponents[6] = new Uint64();
	mComponents[7] = new IA5String();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[8];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return new ServiceId();
	    case 2:
		return AlarmType.faultAlarm;
	    case 3:
		return AlarmCode.illegalCertificate;
	    case 4:
		return AlarmStatus.alarmRaised;
	    case 5:
		return new Uint64();
	    case 6:
		return new Uint64();
	    case 7:
		return new IA5String();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "serviceId"
    public ServiceId getServiceId()
    {
	return (ServiceId)mComponents[1];
    }
    
    public void setServiceId(ServiceId serviceId)
    {
	mComponents[1] = serviceId;
    }
    
    
    // Methods for field "alarmType"
    public AlarmType getAlarmType()
    {
	return (AlarmType)mComponents[2];
    }
    
    public void setAlarmType(AlarmType alarmType)
    {
	mComponents[2] = alarmType;
    }
    
    
    // Methods for field "alarmCode"
    public AlarmCode getAlarmCode()
    {
	return (AlarmCode)mComponents[3];
    }
    
    public void setAlarmCode(AlarmCode alarmCode)
    {
	mComponents[3] = alarmCode;
    }
    
    
    // Methods for field "alarmStatus"
    public AlarmStatus getAlarmStatus()
    {
	return (AlarmStatus)mComponents[4];
    }
    
    public void setAlarmStatus(AlarmStatus alarmStatus)
    {
	mComponents[4] = alarmStatus;
    }
    
    
    // Methods for field "happenTime"
    public Uint64 getHappenTime()
    {
	return (Uint64)mComponents[5];
    }
    
    public void setHappenTime(Uint64 happenTime)
    {
	mComponents[5] = happenTime;
    }
    
    public boolean hasHappenTime()
    {
	return componentIsPresent(5);
    }
    
    public void deleteHappenTime()
    {
	setComponentAbsent(5);
    }
    
    
    // Methods for field "resolvedTime"
    public Uint64 getResolvedTime()
    {
	return (Uint64)mComponents[6];
    }
    
    public void setResolvedTime(Uint64 resolvedTime)
    {
	mComponents[6] = resolvedTime;
    }
    
    public boolean hasResolvedTime()
    {
	return componentIsPresent(6);
    }
    
    public void deleteResolvedTime()
    {
	setComponentAbsent(6);
    }
    
    
    // Methods for field "alarmDesc"
    public IA5String getAlarmDesc()
    {
	return (IA5String)mComponents[7];
    }
    
    public void setAlarmDesc(IA5String alarmDesc)
    {
	mComponents[7] = alarmDesc;
    }
    
    public boolean hasAlarmDesc()
    {
	return componentIsPresent(7);
    }
    
    public void deleteAlarmDesc()
    {
	setComponentAbsent(7);
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "AlarmReportRequest"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "AlarmReportRequest"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					8
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					9
				    ),
				    new MemberListElement (
					"sendPacketStats",
					10
				    ),
				    new MemberListElement (
					"receivePacketStats",
					11
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					12
				    ),
				    new MemberListElement (
					"hostMngService",
					13
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					14
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ServiceId"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ServiceId"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new ServiceId(0), 
				    new ServiceId(32767),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(32767)
			    ),
			    null,
			    2
			)
		    ),
		    "serviceId",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"AlarmType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"AlarmType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"faultAlarm",
					1
				    ),
				    new MemberListElement (
					"securityAlarm",
					2
				    )
				}
			    ),
			    0,
			    AlarmType.faultAlarm
			)
		    ),
		    "alarmType",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"AlarmCode"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"AlarmCode"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"illegalCertificate",
					1
				    ),
				    new MemberListElement (
					"deviceException",
					2
				    ),
				    new MemberListElement (
					"channelException",
					3
				    ),
				    new MemberListElement (
					"protocolVerifyFailure",
					4
				    ),
				    new MemberListElement (
					"keywordCheckFailure",
					5
				    )
				}
			    ),
			    0,
			    AlarmCode.illegalCertificate
			)
		    ),
		    "alarmCode",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"AlarmStatus"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"AlarmStatus"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"alarmRaised",
					0
				    ),
				    new MemberListElement (
					"alarmCleared",
					1
				    )
				}
			    ),
			    0,
			    AlarmStatus.alarmRaised
			)
		    ),
		    "alarmStatus",
		    4,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8005
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "happenTime",
		    5,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8006
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "resolvedTime",
		    6,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new KMCStringInfo (
			    new Tags (
				new short[] {
				    (short)0x8007
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"IA5String"
			    ),
			    new QName (
				"builtin",
				"IA5String"
			    ),
			    536603,
			    new Intersection (
				new SizeConstraint (
				    new ValueRangeConstraint (
					new AbstractBounds(
					    new com.oss.asn1.INTEGER(0), 
					    new com.oss.asn1.INTEGER(200),
					    0
					)
				    )
				),
				new PermittedAlphabetConstraint (
				    IA5StringPAInfo.pa
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(200)
			    ),
			    null
			)
		    ),
		    "alarmDesc",
		    7,
		    3,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8005, 5),
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8007, 7)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' AlarmReportRequest object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' AlarmReportRequest object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for AlarmReportRequest
