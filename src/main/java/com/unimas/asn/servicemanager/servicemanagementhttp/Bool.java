/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue <PERSON>  3 10:13:55 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Bool ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see BOOLEAN
 */

public class Bool extends BOOLEAN {
    
    /**
     * The default constructor.
     */
    public Bool()
    {
    }
    
    /**
     * Construct from a boolean type.
     * @param value the boolean object to set this object to.
     */
    public Bool(boolean value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final TypeInfo c_typeinfo = new TypeInfo (
	new Tags (
	    new short[] {
		0x0001
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "Bool"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Bool"
	),
	536603,
	null
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' Bool object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' Bool object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for Bool
