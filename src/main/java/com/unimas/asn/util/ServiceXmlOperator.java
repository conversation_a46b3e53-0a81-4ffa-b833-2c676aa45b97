package com.unimas.asn.util;

import com.unimas.asn.bean.UdpConfig;
import com.unimas.asn.bean.SourceDevice;
import com.unimas.asn.util.IPUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.*;
import javax.xml.parsers.*;
import javax.xml.transform.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

public class ServiceXmlOperator {
    private static final Logger log = LoggerFactory.getLogger(ServiceXmlOperator.class);
//    private static final String SERVICE_PRE_PATH = "";
//    private static final String SERVICE_FILE_EXT = ".xml";
    private Document document;
    private String filePath;
    private Node targetNode;

    private void createTemplate(String nodeName) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            document = builder.newDocument();
            
            // 创建根节点结构
            document.appendChild(document.createComment(" DOCTYPE preferences SYSTEM 'http://java.sun.com/dtd/preferences.dtd' "));
            
            Element preferences = document.createElement("preferences");
            preferences.setAttribute("EXTERNAL_XML_VERSION", "1.0");
            document.appendChild(preferences);
            
            Element root = document.createElement("root");
            root.setAttribute("type", "system");
            preferences.appendChild(root);
            
            Element rootMap = document.createElement("map");
            root.appendChild(rootMap);
            
            // 创建temporary节点
            Element temporaryNode = document.createElement("node");
            temporaryNode.setAttribute("name", "temporary");
            root.appendChild(temporaryNode);
            
            Element temporaryMap = document.createElement("map");
            temporaryNode.appendChild(temporaryMap);
            
            // 添加temporary节点的固定属性
            Element deployedEntry = document.createElement("entry");
            deployedEntry.setAttribute("key", "deployed");
            deployedEntry.setAttribute("value", "true");
            temporaryMap.appendChild(deployedEntry);
            
            Element stepEntry = document.createElement("entry");
            stepEntry.setAttribute("key", "step");
            stepEntry.setAttribute("value", "ok");
            temporaryMap.appendChild(stepEntry);
            
            Element deployEntry = document.createElement("entry");
            deployEntry.setAttribute("key", "deploy");
            deployEntry.setAttribute("value", "true");
            temporaryMap.appendChild(deployEntry);
            
            // 创建目标节点
            Element node = document.createElement("node");
            node.setAttribute("name", nodeName);
            root.appendChild(node);
            
            Element map = document.createElement("map");
            node.appendChild(map);
            
            targetNode = node;
            saveXml();
        } catch (Exception e) {
            log.error("创建XML模板失败", e);
        }
    }
    public ServiceXmlOperator(String sid){
        this(Constant.SERVICE_PRE_PATH+sid+ Constant.SERVICE_FILE_EXT, sid);
    }
    public ServiceXmlOperator(String filePath, String nodeName) {
        this.filePath = filePath;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                createTemplate(nodeName);
            } else {
                DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                DocumentBuilder builder = factory.newDocumentBuilder();
                document = builder.parse(file);
                findTargetNode(nodeName);
                if (targetNode == null) {
                    // 如果目标节点不存在，在现有文件中创建
                    Element root = (Element) document.getElementsByTagName("root").item(0);
                    Element node = document.createElement("node");
                    node.setAttribute("name", nodeName);
                    root.appendChild(node);
                    Element map = document.createElement("map");
                    node.appendChild(map);
                    targetNode = node;
                    saveXml();
                }
            }
        } catch (Exception e) {
            log.error("初始化XML操作器失败", e);
        }
    }

    private void findTargetNode(String nodeName) {
        NodeList nodeList = document.getElementsByTagName("node");
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (node.getAttributes().getNamedItem("name").getNodeValue().equals(nodeName)) {
                targetNode = node;
                break;
            }
        }
    }

    public Map<String, String> getAttributes() {
        Map<String, String> attributes = new HashMap<>();
        if (targetNode != null) {
            NodeList mapNodes = ((Element) targetNode).getElementsByTagName("map");
            if (mapNodes.getLength() > 0) {
                Element mapElement = (Element) mapNodes.item(0);
                NodeList entries = mapElement.getElementsByTagName("entry");
                for (int i = 0; i < entries.getLength(); i++) {
                    Element entry = (Element) entries.item(i);
                    String key = entry.getAttribute("key");
                    String value = entry.getAttribute("value");
                    attributes.put(key, value);
                }
            }
        }
        return attributes;
    }

    public void setAttribute(String key, String value) {
        if (targetNode != null) {
            NodeList mapNodes = ((Element) targetNode).getElementsByTagName("map");
            if (mapNodes.getLength() > 0) {
                Element mapElement = (Element) mapNodes.item(0);
                NodeList entries = mapElement.getElementsByTagName("entry");
                boolean found = false;
                for (int i = 0; i < entries.getLength(); i++) {
                    Element entry = (Element) entries.item(i);
                    if (entry.getAttribute("key").equals(key)) {
                        entry.setAttribute("value", value);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    Element newEntry = document.createElement("entry");
                    newEntry.setAttribute("key", key);
                    newEntry.setAttribute("value", value);
                    mapElement.appendChild(newEntry);
                }
                saveXml();
            }
        }
    }
    public void setAttributes(Map<String, String> attributes) {
        if (targetNode != null && attributes != null && !attributes.isEmpty()) {
            NodeList mapNodes = ((Element) targetNode).getElementsByTagName("map");
            if (mapNodes.getLength() > 0) {
                Element mapElement = (Element) mapNodes.item(0);
                for (Map.Entry<String, String> entry : attributes.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    boolean found = false;
                    NodeList entries = mapElement.getElementsByTagName("entry");
                    for (int i = 0; i < entries.getLength(); i++) {
                        Element xmlEntry = (Element) entries.item(i);
                        if (xmlEntry.getAttribute("key").equals(key)) {
                            xmlEntry.setAttribute("value", value);
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        Element newEntry = document.createElement("entry");
                        newEntry.setAttribute("key", key);
                        newEntry.setAttribute("value", value);
                        mapElement.appendChild(newEntry);
                    }
                }
                saveXml();
            }
        }
    }

    public void removeAttribute(String key) {
        if (targetNode != null) {
            NodeList mapNodes = ((Element) targetNode).getElementsByTagName("map");
            if (mapNodes.getLength() > 0) {
                Element mapElement = (Element) mapNodes.item(0);
                NodeList entries = mapElement.getElementsByTagName("entry");
                for (int i = 0; i < entries.getLength(); i++) {
                    Element entry = (Element) entries.item(i);
                    if (entry.getAttribute("key").equals(key)) {
                        mapElement.removeChild(entry);
                        saveXml();
                        break;
                    }
                }
            }
        }
    }

    private void saveXml() {
        try {
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            
            // Set properties for XML declaration
            transformer.setOutputProperty(OutputKeys.VERSION, "1.0");
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            transformer.setOutputProperty(OutputKeys.STANDALONE, "no");
            
            // Set indentation
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
            
            DOMSource source = new DOMSource(document);
            StreamResult result = new StreamResult(new File(filePath));
            transformer.transform(source, result);
        } catch (Exception e) {
            log.error("保存XML文件失败", e);
        }
    }
    public void generateProperties(String filePath,UdpConfig config) {
        Properties p = new Properties();
        p.put("serid", config.getSid()); // 服务号

        p.put("g_group", "0");
        p.put("g_ip", "");
        if (config.getIpproxy() != null) {
            p.put("ipproxy", config.getIpproxy()); // 获取广播连接的IP，即传输IP
        }
        p.put("g_cs", "0");
        if(config.getIprange() !=null){
            p.put("iprange", config.getIprange()); // 获取IP地址范围，注意：如果IP范围为"非网段"的话，iprange=##
        }
        if(config.getPortclient() !=null){
            p.put("portclient", config.getPortclient()); // 获取广播端口的端口，即传输端口
        }
        p.put("portchanl", (20560 + Integer.parseInt(config.getSid())) + ""); // 获取channel连接的端口号 (=20560+服务号)
        p.put("audit", "1"); // 是否审计
        p.put("special_s","");//补充额外属性值
        
        // 检测和过滤相关属性
        if (config.getCarcheck() != null) {
            p.put("carcheck", config.getCarcheck());
        }
        if (config.getIdcheck() != null) {
            p.put("idcheck", config.getIdcheck());
        }
        if (config.getCrcfilter() != null) {
            p.put("crcfilter", config.getCrcfilter());
        }
        if (config.getAsnfilter() != null) {
            p.put("asnfilter", config.getAsnfilter());
        }
        if (config.getUnpassdeal() != null) {
            p.put("unpassdeal", config.getUnpassdeal());
        }
        
        // 添加五个新属性
        if (config.getHostip() != null) {
            p.put("hostip", config.getHostip());
        }
        if (config.getHostport() != null) {
            p.put("hostport", config.getHostport());
        }

        try (FileWriter fw = new FileWriter(filePath)) {
            StringBuilder b = new StringBuilder();
            Enumeration<?> propertyNames = p.propertyNames();
            while (propertyNames.hasMoreElements()) {
                String name = propertyNames.nextElement() + "";
                b.append(name).append("=").append(p.getProperty(name)).append("\n");
            }
            fw.write(b.toString());
        } catch (IOException e) {
            log.error("", e);
        }
    }

    /**
     * 从XML文件加载数据到UdpConfig对象
     * @return 包含XML数据的UdpConfig对象
     */
    public UdpConfig loadUdpConfig() {
        UdpConfig config = new UdpConfig();
        Map<String, String> attributes = getAttributes();
        
        // 设置基本属性
        if (attributes.containsKey("sid")) {
            config.setSid(attributes.get("sid"));
        }
        if (attributes.containsKey("displayname")) {
            config.setDisplayname(attributes.get("displayname"));
        }
        if (attributes.containsKey("ipproxy")) {
            config.setIpproxy(attributes.get("ipproxy"));
        }
        if (attributes.containsKey("portclient")) {
            config.setPortclient(attributes.get("portclient"));
        }
        if (attributes.containsKey("iprange")) {
            config.setIprange(attributes.get("iprange"));
        }
        
        // 设置可选属性
        if (attributes.containsKey("eqs")) {
            config.setEqs(attributes.get("eqs"));
        }
        if (attributes.containsKey("weekDay")) {
            config.setWeekDay(attributes.get("weekDay"));
        }
        if (attributes.containsKey("special_value")) {
            config.setSpecial_value(attributes.get("special_value"));
        }
        if (attributes.containsKey("runtime")) {
            config.setRuntime(attributes.get("runtime"));
        }
        if (attributes.containsKey("rules")) {
            config.setRules(attributes.get("rules"));
        }
        if (attributes.containsKey("multicast")) {
            config.setMulticast(attributes.get("multicast"));
        }
        if (attributes.containsKey("srcResLabel")) {
            config.setSrcResLabel(attributes.get("srcResLabel"));
        }
        if (attributes.containsKey("configedtime")) {
            config.setConfigedtime(attributes.get("configedtime"));
        }
        if (attributes.containsKey("audit")) {
            config.setAudit(attributes.get("audit"));
        }
        if (attributes.containsKey("udpFlood")) {
            config.setUdpFlood(attributes.get("udpFlood"));
        }
        if (attributes.containsKey("istemplate")) {
            config.setIstemplate(attributes.get("istemplate"));
        }
        if (attributes.containsKey("proxymode")) {
            config.setProxymode(attributes.get("proxymode"));
        }
        if (attributes.containsKey("multicastip")) {
            config.setMulticastip(attributes.get("multicastip"));
        }
        if (attributes.containsKey("flowlevel")) {
            config.setFlowlevel(attributes.get("flowlevel"));
        }
        
        // 设置检测和过滤相关属性
        if (attributes.containsKey("carcheck")) {
            config.setCarcheck(attributes.get("carcheck"));
        }
        if (attributes.containsKey("idcheck")) {
            config.setIdcheck(attributes.get("idcheck"));
        }
        if (attributes.containsKey("crcfilter")) {
            config.setCrcfilter(attributes.get("crcfilter"));
        }
        if (attributes.containsKey("asnfilter")) {
            config.setAsnfilter(attributes.get("asnfilter"));
        }
        if (attributes.containsKey("unpassdeal")) {
            config.setUnpassdeal(attributes.get("unpassdeal"));
        }
        
        // 设置五个新属性
        if (attributes.containsKey("hostip")) {
            config.setHostip(attributes.get("hostip"));
        }
        if (attributes.containsKey("destResLabel")) {
            config.setDestResLabel(attributes.get("destResLabel"));
        }
        if (attributes.containsKey("sendaddrmap")) {
            config.setSendaddrmap(attributes.get("sendaddrmap"));
        }
        if (attributes.containsKey("hostport")) {
            config.setHostport(attributes.get("hostport"));
        }
        if (attributes.containsKey("srcPort")) {
            config.setSrcPort(attributes.get("srcPort"));
        }
        
        return config;
    }

    /**
     * 将UdpConfig对象保存到XML文件
     * @param config 要保存的UdpConfig对象
     */
    public void saveUdpConfig(UdpConfig config) {
        Map<String, String> attributes = new HashMap<>();
        
        // 添加基本属性
        if (config.getSid() != null) {
            attributes.put("sid", config.getSid());
        }
        if (config.getDisplayname() != null) {
            attributes.put("displayname", config.getDisplayname());
        }
        if (config.getIpproxy() != null) {
            attributes.put("ipproxy", config.getIpproxy());
        }
        if (config.getPortclient() != null) {
            attributes.put("portclient", config.getPortclient());
        }
        if (config.getIprange() != null) {
            attributes.put("iprange", config.getIprange());
        }
        
        // 添加可选属性
        if (config.getEqs() != null) {
            attributes.put("eqs", config.getEqs());
        }
        if (config.getWeekDay() != null) {
            attributes.put("weekDay", config.getWeekDay());
        }
        if (config.getSpecial_value() != null) {
            attributes.put("special_value", config.getSpecial_value());
        }
        if (config.getRuntime() != null) {
            attributes.put("runtime", config.getRuntime());
        }
        if (config.getRules() != null) {
            attributes.put("rules", config.getRules());
        }
        if (config.getMulticast() != null) {
            attributes.put("multicast", config.getMulticast());
        }
        if (config.getSrcResLabel() != null) {
            attributes.put("srcResLabel", config.getSrcResLabel());
        }
        if (config.getConfigedtime() != null) {
            attributes.put("configedtime", config.getConfigedtime());
        }
        if (config.getAudit() != null) {
            attributes.put("audit", config.getAudit());
        }
        if (config.getUdpFlood() != null) {
            attributes.put("udpFlood", config.getUdpFlood());
        }
        if (config.getIstemplate() != null) {
            attributes.put("istemplate", config.getIstemplate());
        }
        if (config.getProxymode() != null) {
            attributes.put("proxymode", config.getProxymode());
        }
        if (config.getMulticastip() != null) {
            attributes.put("multicastip", config.getMulticastip());
        }
        if (config.getFlowlevel() != null) {
            attributes.put("flowlevel", config.getFlowlevel());
        }
        
        // 添加检测和过滤相关属性
        if (config.getCarcheck() != null) {
            attributes.put("carcheck", config.getCarcheck());
        }
        if (config.getIdcheck() != null) {
            attributes.put("idcheck", config.getIdcheck());
        }
        if (config.getCrcfilter() != null) {
            attributes.put("crcfilter", config.getCrcfilter());
        }
        if (config.getAsnfilter() != null) {
            attributes.put("asnfilter", config.getAsnfilter());
        }
        if (config.getUnpassdeal() != null) {
            attributes.put("unpassdeal", config.getUnpassdeal());
        }
        
        // 添加五个新属性
        if (config.getHostip() != null) {
            attributes.put("hostip", config.getHostip());
        }
        if (config.getDestResLabel() != null) {
            attributes.put("destResLabel", config.getDestResLabel());
        }
        if (config.getSendaddrmap() != null) {
            attributes.put("sendaddrmap", config.getSendaddrmap());
        }
        if (config.getHostport() != null) {
            attributes.put("hostport", config.getHostport());
        }
        if (config.getSrcPort() != null) {
            attributes.put("srcPort", config.getSrcPort());
        }
        
        // 将所有属性保存到XML
        setAttributes(attributes);
    }

    public static void main(String[] args) {
        // 使用示例
        ServiceXmlOperator operator = new ServiceXmlOperator("17.xml", "17");
        
        // 加载配置到UdpConfig
        UdpConfig config = operator.loadUdpConfig();
        System.out.println("Loaded displayname: " + config.getDisplayname());
        
        // 修改UdpConfig
        config.setIpproxy("***********");
        config.setPortclient("8080");
        
        // 保存回XML
        operator.saveUdpConfig(config);
        System.out.println("Config saved successfully!");
        
        // 获取所有属性
        Map<String, String> attributes = operator.getAttributes();
        System.out.println("Current attributes: " + attributes);
    }
}